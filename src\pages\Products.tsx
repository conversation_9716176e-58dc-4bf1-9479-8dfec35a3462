
const Products = () => {
  const products = [
    {
      name: "LumenCloud",
      description: "Enterprise cloud infrastructure platform",
      category: "Infrastructure",
      status: "Live"
    },
    {
      name: "LumenMail",
      description: "Professional email hosting service",
      category: "Communication",
      status: "Live"
    },
    {
      name: "LumenSecure",
      description: "Cybersecurity monitoring and response",
      category: "Security",
      status: "Live"
    },
    {
      name: "LumenFlow",
      description: "Business process automation platform",
      category: "Automation",
      status: "Beta"
    }
  ];

  const companies = [
    {
      name: "LumenWorks Digital",
      description: "Web development and digital marketing",
      focus: "Small to medium businesses"
    },
    {
      name: "LumenWorks Enterprise",
      description: "Enterprise cloud solutions and consulting",
      focus: "Large corporations and government"
    },
    {
      name: "LumenWorks Security",
      description: "Cybersecurity services and compliance",
      focus: "Regulated industries"
    }
  ];

  return (
    <div className="min-h-screen bg-white pt-20">
      <div className="py-20 lg:py-28">
        <div className="max-w-content mx-auto px-6">
          <div className="text-center mb-16">
            <h1 className="text-4xl lg:text-5xl font-bold text-lumen-charcoal section-beam mb-8">
              Our Products & Companies
            </h1>
            <p className="text-xl text-lumen-mid-gray max-w-3xl mx-auto">
              A comprehensive ecosystem of technology solutions and specialized companies.
            </p>
          </div>

          {/* Products Section */}
          <div className="mb-20">
            <h2 className="text-3xl font-bold text-lumen-charcoal mb-12 text-center">Products</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {products.map((product, index) => (
                <div key={index} className="bg-white rounded-3xl p-8 border border-lumen-yellow/20 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                  <div className="flex justify-between items-start mb-4">
                    <h3 className="text-xl font-bold text-lumen-charcoal">{product.name}</h3>
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                      product.status === 'Live' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {product.status}
                    </span>
                  </div>
                  <p className="text-sm text-lumen-mid-gray mb-4">{product.description}</p>
                  <span className="text-xs text-lumen-charcoal bg-lumen-yellow/20 px-2 py-1 rounded">
                    {product.category}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Companies Section */}
          <div>
            <h2 className="text-3xl font-bold text-lumen-charcoal mb-12 text-center">Companies</h2>
            <div className="grid md:grid-cols-3 gap-8">
              {companies.map((company, index) => (
                <div key={index} className="bg-gradient-to-br from-lumen-off-white to-lumen-yellow/10 rounded-3xl p-8">
                  <h3 className="text-xl font-bold text-lumen-charcoal mb-4">{company.name}</h3>
                  <p className="text-lumen-mid-gray mb-4">{company.description}</p>
                  <p className="text-sm font-medium text-lumen-charcoal">Focus: {company.focus}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Products;
