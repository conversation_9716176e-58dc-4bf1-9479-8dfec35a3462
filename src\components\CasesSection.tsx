
import { Button } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";

const CasesSection = () => {
  const featuredCases = [
    {
      title: "E-commerce Platform Overhaul",
      client: "RetailCorp",
      result: "120% revenue increase",
      metric: "0.7s load time",
      industry: "Retail"
    },
    {
      title: "Cloud Migration & Scaling",
      client: "TechStartup Inc",
      result: "99.99% uptime achieved",
      metric: "10x traffic handled",
      industry: "Technology"
    },
    {
      title: "Email Deliverability Recovery",
      client: "MarketingPro",
      result: "99.8% inbox delivery",
      metric: "300% open rate increase",
      industry: "Marketing"
    }
  ];

  return (
    <section className="w-full bg-white py-20 lg:py-28">
      <div className="max-w-content mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-lumen-charcoal section-beam mb-8">
            Success Stories
          </h2>
          <p className="text-xl text-lumen-mid-gray max-w-3xl mx-auto leading-relaxed">
            Real results from real businesses. See how we've transformed technology challenges into growth opportunities.
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8 mb-12">
          {featuredCases.map((caseStudy, index) => (
            <div 
              key={index} 
              className="group bg-gradient-to-br from-lumen-off-white via-white to-lumen-yellow/5 rounded-3xl p-8 border border-lumen-yellow/20 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2"
            >
              <div className="flex justify-between items-start mb-4">
                <span className="px-3 py-1 bg-lumen-yellow/20 text-lumen-charcoal text-xs font-medium rounded-full">
                  {caseStudy.industry}
                </span>
              </div>
              <h3 className="text-xl font-bold text-lumen-charcoal mb-2 group-hover:text-lumen-yellow-hover transition-colors duration-300">
                {caseStudy.title}
              </h3>
              <p className="text-lumen-mid-gray mb-4">{caseStudy.client}</p>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-lumen-mid-gray">Result:</span>
                  <span className="text-sm font-semibold text-lumen-charcoal">{caseStudy.result}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-lumen-mid-gray">Key Metric:</span>
                  <span className="text-sm font-semibold text-lumen-charcoal">{caseStudy.metric}</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center">
          <Link to="/cases">
            <Button variant="outline" className="border-2 border-lumen-yellow/30 hover:border-lumen-yellow text-lumen-charcoal font-bold px-8 py-3">
              View All Case Studies
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default CasesSection;
