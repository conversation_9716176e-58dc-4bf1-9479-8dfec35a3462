
import Header from "@/components/Header";
import Footer from "@/components/Footer";

const Team = () => {
  const teamMembers = [
    {
      name: "<PERSON>",
      role: "CEO & Founder",
      bio: "15 years of experience in enterprise technology solutions",
      image: "/placeholder.svg"
    },
    {
      name: "<PERSON>",
      role: "<PERSON><PERSON>",
      bio: "Former Google engineer specializing in cloud architecture",
      image: "/placeholder.svg"
    },
    {
      name: "<PERSON>",
      role: "Head of Security",
      bio: "Cybersecurity expert with government and Fortune 500 experience",
      image: "/placeholder.svg"
    },
    {
      name: "<PERSON>",
      role: "Lead Developer",
      bio: "Full-stack developer passionate about performance optimization",
      image: "/placeholder.svg"
    },
    {
      name: "<PERSON>",
      role: "Head of Client Success",
      bio: "Dedicated to ensuring client satisfaction and business growth",
      image: "/placeholder.svg"
    },
    {
      name: "<PERSON>",
      role: "Infrastructure Specialist",
      bio: "Cloud infrastructure expert with 12+ years of experience",
      image: "/placeholder.svg"
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      <Header />
      <div className="py-20 lg:py-28 pt-20">
        <div className="max-w-content mx-auto px-6">
          <div className="text-center mb-16">
            <h1 className="text-4xl lg:text-5xl font-bold text-lumen-charcoal section-beam mb-8">
              Our Team
            </h1>
            <p className="text-xl text-lumen-mid-gray max-w-3xl mx-auto">
              Meet the experts who make your technology challenges disappear.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-12">
            {teamMembers.map((member, index) => (
              <div key={index} className="text-center group">
                <div className="relative mb-6">
                  <div className="w-48 h-48 mx-auto rounded-full bg-gradient-to-br from-lumen-yellow/20 to-lumen-yellow/10 flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                    <img
                      src={member.image}
                      alt={member.name}
                      className="w-40 h-40 rounded-full object-cover"
                    />
                  </div>
                </div>
                <h3 className="text-xl font-bold text-lumen-charcoal mb-2">{member.name}</h3>
                <p className="text-lumen-yellow-hover font-medium mb-3">{member.role}</p>
                <p className="text-lumen-mid-gray text-sm leading-relaxed">{member.bio}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Team;
