
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { CheckCircle } from "lucide-react";

interface ServicePageProps {
  title: string;
  subtitle: string;
  description: string;
  icon: string;
  commitment: string;
  features: string[];
  benefits: string[];
}

const ServicePageTemplate = ({
  title,
  subtitle,
  description,
  icon,
  commitment,
  features,
  benefits
}: ServicePageProps) => {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      {/* Hero Section */}
      <section className="py-20 lg:py-28 pt-20 bg-gradient-to-br from-lumen-off-white via-white to-lumen-yellow/5">
        <div className="max-w-content mx-auto px-6 text-center">
          <div className="text-6xl mb-6">{icon}</div>
          <h1 className="text-4xl lg:text-5xl font-bold text-lumen-charcoal mb-4">
            {title}
          </h1>
          <p className="text-xl text-lumen-mid-gray mb-6 max-w-2xl mx-auto">
            {subtitle}
          </p>
          <div className="inline-block px-4 py-2 bg-lumen-yellow/20 rounded-full border border-lumen-yellow/30 backdrop-blur-sm mb-8">
            <span className="text-sm font-medium text-lumen-charcoal">{commitment}</span>
          </div>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-lumen-yellow hover:bg-lumen-yellow-hover text-lumen-charcoal font-bold px-10 py-6 text-lg">
              Get Started Today
            </Button>
            <Button variant="outline" size="lg" className="border-2 border-lumen-yellow/30 hover:border-lumen-yellow text-lumen-charcoal font-bold px-8 py-6 text-lg">
              Book Free Consultation
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-content mx-auto px-6">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-3xl font-bold text-lumen-charcoal mb-6">
                What you get
              </h2>
              <p className="text-lg text-lumen-mid-gray mb-8">{description}</p>
              <ul className="space-y-4">
                {features.map((feature, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <CheckCircle className="h-6 w-6 text-lumen-yellow flex-shrink-0 mt-0.5" />
                    <span className="text-lumen-charcoal">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div className="bg-gradient-to-br from-lumen-off-white to-lumen-yellow/10 rounded-3xl p-8">
              <h3 className="text-2xl font-bold text-lumen-charcoal mb-6">Why choose us?</h3>
              <ul className="space-y-4">
                {benefits.map((benefit, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-lumen-yellow flex-shrink-0 mt-0.5" />
                    <span className="text-lumen-charcoal text-sm">{benefit}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-lumen-yellow/10 to-lumen-yellow/5">
        <div className="max-w-content mx-auto px-6 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold text-lumen-charcoal mb-6">
            Ready to get started?
          </h2>
          <p className="text-lg text-lumen-mid-gray mb-8 max-w-2xl mx-auto">
            Let's discuss how we can help streamline your {title.toLowerCase()} needs.
          </p>
          <Button size="lg" className="bg-lumen-yellow hover:bg-lumen-yellow-hover text-lumen-charcoal font-bold px-12 py-6 text-lg">
            Book Your Free Consultation
          </Button>
        </div>
      </section>
      <Footer />
    </div>
  );
};

export default ServicePageTemplate;
