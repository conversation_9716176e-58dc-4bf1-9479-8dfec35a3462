
const Cases = () => {
  const cases = [
    {
      title: "E-commerce Platform Overhaul",
      client: "RetailCorp",
      challenge: "Slow website causing 40% cart abandonment",
      solution: "Complete platform rebuild with performance optimization",
      results: ["Page load time reduced from 8s to 0.7s", "Cart abandonment down 65%", "Revenue increased 120%"],
      industry: "Retail"
    },
    {
      title: "Cloud Migration & Scaling",
      client: "TechStartup Inc",
      challenge: "Infrastructure couldn't handle rapid user growth",
      solution: "Cloud-native architecture with auto-scaling",
      results: ["99.99% uptime achieved", "Handled 10x traffic increase", "Infrastructure costs reduced 40%"],
      industry: "Technology"
    },
    {
      title: "Email Deliverability Recovery",
      client: "MarketingPro",
      challenge: "70% of emails going to spam folders",
      solution: "Email infrastructure rebuild and reputation management",
      results: ["Inbox delivery rate: 99.8%", "Open rates increased 300%", "Customer engagement up 250%"],
      industry: "Marketing"
    },
    {
      title: "Process Automation System",
      client: "ManufacturingCorp",
      challenge: "Manual inventory management causing errors",
      solution: "Custom automation platform with real-time tracking",
      results: ["Manual tasks reduced 80%", "Inventory errors down 95%", "Staff productivity up 150%"],
      industry: "Manufacturing"
    }
  ];

  return (
    <div className="min-h-screen bg-white pt-20">
      <div className="py-20 lg:py-28">
        <div className="max-w-content mx-auto px-6">
          <div className="text-center mb-16">
            <h1 className="text-4xl lg:text-5xl font-bold text-lumen-charcoal section-beam mb-8">
              Success Stories
            </h1>
            <p className="text-xl text-lumen-mid-gray max-w-3xl mx-auto">
              Real results from real businesses. See how we've helped companies transform their technology.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            {cases.map((caseStudy, index) => (
              <div key={index} className="bg-white rounded-3xl p-8 border border-lumen-yellow/20 shadow-lg hover:shadow-2xl transition-all duration-300">
                <div className="flex justify-between items-start mb-6">
                  <div>
                    <h3 className="text-2xl font-bold text-lumen-charcoal mb-2">{caseStudy.title}</h3>
                    <p className="text-lumen-mid-gray">{caseStudy.client}</p>
                  </div>
                  <span className="px-3 py-1 bg-lumen-yellow/20 text-lumen-charcoal text-xs font-medium rounded-full">
                    {caseStudy.industry}
                  </span>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-lumen-charcoal mb-2">Challenge</h4>
                    <p className="text-lumen-mid-gray text-sm">{caseStudy.challenge}</p>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold text-lumen-charcoal mb-2">Solution</h4>
                    <p className="text-lumen-mid-gray text-sm">{caseStudy.solution}</p>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold text-lumen-charcoal mb-2">Results</h4>
                    <ul className="space-y-1">
                      {caseStudy.results.map((result, idx) => (
                        <li key={idx} className="text-lumen-mid-gray text-sm flex items-center">
                          <span className="w-2 h-2 bg-lumen-yellow rounded-full mr-2"></span>
                          {result}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Cases;
