
import { Button } from "@/components/ui/button";

const Contact = () => {
  return (
    <div className="min-h-screen bg-white pt-20">
      <div className="py-20 lg:py-28">
        <div className="max-w-content mx-auto px-6">
          <div className="text-center mb-16">
            <h1 className="text-4xl lg:text-5xl font-bold text-lumen-charcoal section-beam mb-8">
              Get In Touch
            </h1>
            <p className="text-xl text-lumen-mid-gray max-w-3xl mx-auto">
              Ready to transform your technology? Let's start the conversation.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-16">
            {/* Contact Form */}
            <div className="bg-white rounded-3xl p-8 border border-lumen-yellow/20 shadow-lg">
              <h2 className="text-2xl font-bold text-lumen-charcoal mb-6">Send us a message</h2>
              <form className="space-y-6">
                <div className="grid md:grid-cols-2 gap-4">
                  <input
                    type="text"
                    placeholder="First Name"
                    className="w-full px-4 py-3 border border-lumen-yellow/30 rounded-lg focus:outline-none focus:border-lumen-yellow"
                  />
                  <input
                    type="text"
                    placeholder="Last Name"
                    className="w-full px-4 py-3 border border-lumen-yellow/30 rounded-lg focus:outline-none focus:border-lumen-yellow"
                  />
                </div>
                <input
                  type="email"
                  placeholder="Email Address"
                  className="w-full px-4 py-3 border border-lumen-yellow/30 rounded-lg focus:outline-none focus:border-lumen-yellow"
                />
                <input
                  type="text"
                  placeholder="Company"
                  className="w-full px-4 py-3 border border-lumen-yellow/30 rounded-lg focus:outline-none focus:border-lumen-yellow"
                />
                <textarea
                  placeholder="Tell us about your project..."
                  rows={6}
                  className="w-full px-4 py-3 border border-lumen-yellow/30 rounded-lg focus:outline-none focus:border-lumen-yellow resize-none"
                ></textarea>
                <Button className="w-full bg-lumen-yellow hover:bg-lumen-yellow-hover text-lumen-charcoal font-bold py-3">
                  Send Message
                </Button>
              </form>
            </div>

            {/* Contact Information */}
            <div className="space-y-8">
              <div className="bg-gradient-to-br from-lumen-off-white to-lumen-yellow/10 rounded-3xl p-8">
                <h3 className="text-xl font-bold text-lumen-charcoal mb-4">Let's talk</h3>
                <p className="text-lumen-mid-gray mb-6">
                  Ready to discuss your project? Book a free consultation and discover how we can help.
                </p>
                <Button className="bg-lumen-yellow hover:bg-lumen-yellow-hover text-lumen-charcoal font-bold">
                  Book Free Consultation
                </Button>
              </div>

              <div className="space-y-6">
                <div>
                  <h4 className="font-semibold text-lumen-charcoal mb-2">Email</h4>
                  <p className="text-lumen-mid-gray"><EMAIL></p>
                </div>
                <div>
                  <h4 className="font-semibold text-lumen-charcoal mb-2">Phone</h4>
                  <p className="text-lumen-mid-gray">+****************</p>
                </div>
                <div>
                  <h4 className="font-semibold text-lumen-charcoal mb-2">Office</h4>
                  <p className="text-lumen-mid-gray">
                    123 Tech Street<br />
                    San Francisco, CA 94105
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold text-lumen-charcoal mb-2">Response Time</h4>
                  <p className="text-lumen-mid-gray">We typically respond within 2 hours during business hours</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Contact;
