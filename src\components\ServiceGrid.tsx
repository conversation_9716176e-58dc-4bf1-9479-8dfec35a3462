
const ServiceGrid = () => {
  const services = [
    {
      title: "Web & Digital",
      commitment: "pages < 1s, 99.9% uptime",
      description: "Fast-loading websites and web apps that stay online when your customers need them.",
      icon: "🌐"
    },
    {
      title: "Cloud Infrastructure",
      commitment: "autoscale < 40s, backups every 15 min",
      description: "Servers that grow with your business and protect your data automatically.",
      icon: "☁️"
    },
    {
      title: "Email & Communication",
      commitment: "99.99% inbox delivery",
      description: "Email systems that actually reach your customers' inboxes, every time.",
      icon: "📧"
    },
    {
      title: "Internal Software & Automation",
      commitment: "tasks cut ≥ 30%",
      description: "Custom tools that eliminate repetitive work and streamline your operations.",
      icon: "⚡"
    },
    {
      title: "Security & Compliance",
      commitment: "critical fixes ≤ 72h",
      description: "Protection against threats with rapid response when issues arise.",
      icon: "🔒"
    },
    {
      title: "Tech Consulting",
      commitment: "30-day roadmap",
      description: "Clear technology strategy that aligns with your business goals.",
      icon: "🎯"
    },
    {
      title: "Support & Maintenance",
      commitment: "30-min response",
      description: "Expert help when you need it, with quick response times that keep you moving.",
      icon: "🛠️"
    }
  ];

  return (
    <section id="services" className="w-full bg-gradient-to-br from-lumen-off-white via-white to-lumen-off-white py-20 lg:py-28 relative">
      <div className="absolute inset-0 bg-gradient-to-br from-lumen-yellow/3 via-transparent to-lumen-yellow/5"></div>
      <div className="max-w-content mx-auto px-6 relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-lumen-charcoal section-beam mb-8">
            What we deliver
          </h2>
          <p className="text-xl text-lumen-mid-gray max-w-3xl mx-auto leading-relaxed">
            Seven core pillars that keep your business running smoothly, with measurable commitments you can count on.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {services.map((service, index) => (
            <div 
              key={index} 
              className={`group bg-white/80 backdrop-blur-sm rounded-3xl p-8 border border-lumen-yellow/20 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 hover:border-lumen-yellow/40 ${
                index === 6 ? 'md:col-span-2 lg:col-span-1 xl:col-span-2 lg:mx-auto xl:mx-0' : ''
              }`}
            >
              <div className="text-3xl mb-4 group-hover:scale-110 transition-transform duration-300">
                {service.icon}
              </div>
              <h3 className="text-xl font-bold text-lumen-charcoal mb-3 group-hover:text-lumen-yellow-hover transition-colors duration-300">
                {service.title}
              </h3>
              <div className="text-sm font-mono text-lumen-charcoal mb-4 bg-gradient-to-r from-lumen-yellow/20 to-lumen-yellow/10 px-3 py-2 rounded-xl border border-lumen-yellow/30">
                {service.commitment}
              </div>
              <p className="text-sm text-lumen-mid-gray leading-relaxed">
                {service.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServiceGrid;
